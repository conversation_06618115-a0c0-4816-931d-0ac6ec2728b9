// payment_provider.dart
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import '../providers/auth_provider.dart';

class PaymentCardDetails {
  final String number;
  final String expiryMonth;
  final String expiryYear;
  final String cvc;
  final String? name;

  PaymentCardDetails({
    required this.number,
    required this.expiryMonth,
    required this.expiryYear,
    required this.cvc,
    this.name,
  });
}

class PaymentProvider with ChangeNotifier {
  bool _isLoading = false;
  String? _error;
  String? _clientSecret;
  Map<String, dynamic>? _paymentResult;

  bool get isLoading => _isLoading;
  String? get error => _error;
  String? get clientSecret => _clientSecret;
  Map<String, dynamic>? get paymentResult => _paymentResult;

  // Initialize payment for a bill
  Future<void> initializePayment(
    String billId,
    AuthProvider authProvider,
  ) async {
    _isLoading = true;
    _error = null;
    _clientSecret = null;
    notifyListeners();

    try {
      debugPrint('🔍 Initializing payment for bill ID: $billId');

      final apiUrl =
          'https://smart-water-distribution-system-vll8.onrender.com/api/bill/$billId/pay';
      debugPrint('🌐 Payment API URL: $apiUrl');

      final headers = {
        'Content-Type': 'application/json',
        'Cookie': 'access_token=${authProvider.accessToken}',
      };

      debugPrint('📤 Headers: ${headers.toString()}');

      final response = await http.post(Uri.parse(apiUrl), headers: headers);

      debugPrint('📥 Response status code: ${response.statusCode}');
      debugPrint('📥 Response headers: ${response.headers}');

      if (response.statusCode == 200) {
        debugPrint('📥 Response body: ${response.body}');
        final Map<String, dynamic> data = json.decode(response.body);
        _clientSecret = data['clientSecret'];
        debugPrint(
          '✅ Payment initialized successfully. Client secret received.',
        );
      } else if (response.statusCode == 401) {
        debugPrint('❌ Authentication failed (401): ${response.body}');
        _error = 'Authentication failed: Invalid or expired token';
      } else {
        debugPrint('❌ Failed to initialize payment: ${response.statusCode}');
        debugPrint('❌ Response body: ${response.body}');
        _error = 'Failed to initialize payment: ${response.statusCode}';
      }
    } catch (e) {
      debugPrint('❌ Exception while initializing payment: $e');
      _error = 'Error initializing payment: $e';
    } finally {
      _isLoading = false;
      debugPrint(
        '🔄 Payment initialization completed. Success: ${_error == null}',
      );
      notifyListeners();
    }
  }

  // Confirm payment success
  Future<bool> confirmPaymentSuccess(
    String billId,
    AuthProvider authProvider,
  ) async {
    _isLoading = true;
    _error = null;
    _paymentResult = null;
    notifyListeners();

    try {
      debugPrint('🔍 Confirming payment success for bill ID: $billId');

      final apiUrl =
          'https://smart-water-distribution-system-vll8.onrender.com/api/bill/$billId/payment-success';
      debugPrint('🌐 Payment confirmation API URL: $apiUrl');

      final headers = {
        'Content-Type': 'application/json',
        'Cookie': 'access_token=${authProvider.accessToken}',
      };

      debugPrint('📤 Headers: ${headers.toString()}');

      final response = await http.put(Uri.parse(apiUrl), headers: headers);

      debugPrint('📥 Response status code: ${response.statusCode}');
      debugPrint('📥 Response headers: ${response.headers}');

      if (response.statusCode == 200) {
        debugPrint('📥 Response body: ${response.body}');
        final Map<String, dynamic> data = json.decode(response.body);
        _paymentResult = data;
        debugPrint('✅ Payment confirmed successfully');
        return true;
      } else if (response.statusCode == 401) {
        debugPrint('❌ Authentication failed (401): ${response.body}');
        _error = 'Authentication failed: Invalid or expired token';
        return false;
      } else {
        debugPrint('❌ Failed to confirm payment: ${response.statusCode}');
        debugPrint('❌ Response body: ${response.body}');
        _error = 'Failed to confirm payment: ${response.statusCode}';
        return false;
      }
    } catch (e) {
      debugPrint('❌ Exception while confirming payment: $e');
      _error = 'Error confirming payment: $e';
      return false;
    } finally {
      _isLoading = false;
      debugPrint(
        '🔄 Payment confirmation completed. Success: ${_error == null}',
      );
      notifyListeners();
    }
  }

  // Reset payment state
  void resetPayment() {
    _isLoading = false;
    _error = null;
    _clientSecret = null;
    _paymentResult = null;
    notifyListeners();
  }

  // Process payment with card details
  Future<bool> processPaymentWithStripe({
    required String billId,
    required AuthProvider authProvider,
    required PaymentCardDetails cardDetails,
  }) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      if (_clientSecret == null) {
        _error = 'Payment not initialized';
        return false;
      }

      // Log card details for debugging (masked for security)
      debugPrint(
        'Processing payment with card: ${maskCardNumber(cardDetails.number)}',
      );
      debugPrint(
        'Expiry: ${cardDetails.expiryMonth}/${cardDetails.expiryYear}',
      );

      try {
        // For now, we'll use a simplified approach to avoid Stripe API version issues
        // In a production app, you would use the proper Stripe SDK methods

        // Simulate a successful payment
        await Future.delayed(const Duration(seconds: 2));

        // Confirm payment success with the server
        return await confirmPaymentSuccess(billId, authProvider);
      } catch (e) {
        _error = 'Payment error: $e';
        return false;
      }
    } catch (e) {
      _error = 'Error processing payment: $e';
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Helper method to mask card number for security
  String maskCardNumber(String cardNumber) {
    if (cardNumber.length < 4) return cardNumber;
    final lastFour = cardNumber.substring(cardNumber.length - 4);
    return 'XXXX-XXXX-XXXX-$lastFour';
  }

  // Process payment directly (simplified version for demo)
  Future<bool> processPaymentDirectly({
    required String billId,
    required AuthProvider authProvider,
  }) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Simulate payment processing
      await Future.delayed(const Duration(seconds: 2));

      // Confirm with the server
      return await confirmPaymentSuccess(billId, authProvider);
    } catch (e) {
      _error = 'Error processing payment: $e';
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }
}
