<manifest xmlns:android="http://schemas.android.com/apk/res/android">

    <!-- 1) Camera permission -->
    <uses-permission android:name="android.permission.CAMERA" />

    <!-- 2) Read External Storage (for older Android versions) -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />

    <!-- 3) Write External Storage (mostly deprecated from Android 10+, optional if needed) -->
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />

    <!-- 4) For Android 13+ media access (if you only need images) -->
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />

    <!-- (Optional) If you need location or other permissions, also declare them here. -->
    <!-- <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" /> -->

    <application
        android:label="Smart Tank"
        android:name="${applicationName}"
        android:icon="@mipmap/ic_launcher"
        android:enableOnBackInvokedCallback="true">
        <activity
            android:name="com.smarttank.app.MainActivity"
            android:exported="true"
            android:launchMode="singleTop"
            android:taskAffinity=""
            android:theme="@style/Theme.AppCompat.Light.NoActionBar"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:windowSoftInputMode="adjustResize">

            <meta-data
                android:name="io.flutter.embedding.android.NormalTheme"
                android:resource="@style/NormalTheme" />

            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <meta-data
            android:name="flutterEmbedding"
            android:value="2" />
    </application>

    <queries>
        <intent>
            <action android:name="android.intent.action.PROCESS_TEXT" />
            <data android:mimeType="text/plain" />
        </intent>
    </queries>
</manifest>
